use std::cmp::Ordering;
use std::collections::HashMap;
use std::io::Write;

use indexmap::IndexMap;

pub fn save_log(
    command: &str,
    host_name: &str,
    dest_file: &str,
    msg: &str,
    echo: bool,
) -> std::io::Result<()> {
    let now = chrono::Local::now();
    let ymdhms = now.format("%Y-%m-%d %H:%M:%S").to_string();

    if echo {
        println!("{} {} {} {}", host_name, command, ymdhms, msg);
    }

    let mut file = std::fs::OpenOptions::new()
        .append(true)
        .create(true)
        .open(dest_file)?;

    writeln!(file, "{} {}", ymdhms, msg)?;
    Ok(())
}


/// Sort a dictionary (HashMap) based on its keys or values.
///
/// # Parameters
/// - `dictionary`: A reference to the HashMap to be sorted.
/// - `based_on`: The criteria to sort by, either `"key"` or `"value"`.
/// - `reverse`: If `true`, sort in descending order; otherwise ascending.
///
/// # Returns
/// An `IndexMap<K, V>` containing the items sorted based on the specified criteria.
/// Preserves insertion order like Python's `dict`. If `based_on` is invalid, returns the original order.
///
/// # Examples
/// ```
/// let mut map = HashMap::new();
/// map.insert("b", 2);
/// map.insert("a", 1);
/// map.insert("c", 3);
///
/// let sorted = sort_dict(&map, "key", false);
/// // sorted: [("a", 1), ("b", 2), ("c", 3)]
///
/// let sorted = sort_dict(&map, "value", true);
/// // sorted: [("c", 3), ("b", 2), ("a", 1)]
/// ```
pub fn sort_dict<K, V>(dictionary: &HashMap<K, V>, based_on: &str, reverse: bool) -> IndexMap<K, V>
where
    K: Ord + Clone + std::hash::Hash + Eq,
    V: Ord + Clone,
{
    match based_on {
        "key" | "value" => {
            let mut items: Vec<(K, V)> = dictionary.iter().map(|(k, v)| (k.clone(), v.clone())).collect();

            match based_on {
                "key" => {
                    items.sort_by(|a, b| {
                        if reverse { b.0.cmp(&a.0) } else { a.0.cmp(&b.0) }
                    });
                }
                "value" => {
                    items.sort_by(|a, b| {
                        if reverse { b.1.cmp(&a.1) } else { a.1.cmp(&b.1) }
                    });
                }
                _ => unreachable!(), // We've already matched the valid cases
            }

            items.into_iter().collect()
        }

        _ => {
            // Return the original unsorted dictionary
            dictionary.iter().map(|(k, v)| (k.clone(), v.clone())).collect()
        }
    }
}
